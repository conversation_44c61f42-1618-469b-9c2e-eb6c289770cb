package com.cheche365.bc.util;

import com.cheche365.bc.entity.hbase.ActionLog;
import com.cheche365.bc.entity.hbase.AutoTaskLog;
import org.apache.hadoop.hbase.util.Bytes;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * HBase模型工具类
 * 提供模型与HBase数据转换的工具方法
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
public class HBaseModelUtil {

    /**
     * 日期时间格式化器
     */
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 将AutoTaskLog转换为HBase的列值Map
     *
     * @param autoTaskLog AutoTaskLog对象
     * @return 列族->列名->值的Map
     */
    public static Map<String, Map<String, byte[]>> convertAutoTaskLogToHBaseData(AutoTaskLog autoTaskLog) {
        Map<String, Map<String, byte[]>> familyMap = new HashMap<>();

        // content列族数据
        Map<String, byte[]> contentData = new HashMap<>();
        putIfNotNull(contentData, "applyJson", autoTaskLog.getApplyJson());
        putIfNotNull(contentData, "feedbackJson", autoTaskLog.getFeedbackJson());
        putIfNotNull(contentData, "resultStr", autoTaskLog.getResultStr());

        familyMap.put(AutoTaskLog.CF_DATA, contentData);

        return familyMap;
    }

    /**
     * 将ActionLog转换为HBase的列值Map
     *
     * @param actionLog ActionLog对象
     * @return 列族->列名->值的Map
     */
    public static Map<String, Map<String, byte[]>> convertActionLogToHBaseData(ActionLog actionLog) {
        Map<String, Map<String, byte[]>> familyMap = new HashMap<>();

        // info列族数据
        Map<String, byte[]> infoData = new HashMap<>();
        putIfNotNull(infoData, "actionName", actionLog.getActionName());
        putIfNotNull(infoData, "url", actionLog.getUrl());
        putIfNotNull(infoData, "sendTime", formatDateTime(actionLog.getSendTime()));
        putIfNotNull(infoData, "receiveTime", formatDateTime(actionLog.getReceiveTime()));

        familyMap.put(ActionLog.CF_INFO, infoData);

        // data列族数据
        Map<String, byte[]> contentData = new HashMap<>();
        putIfNotNull(contentData, "requestBody", actionLog.getRequestBody());
        putIfNotNull(contentData, "responseBody", actionLog.getResponseBody());
        putIfNotNull(contentData, "inTaskBody", actionLog.getInTaskBody());
        putIfNotNull(contentData, "outTaskBody", actionLog.getOutTaskBody());

        familyMap.put(ActionLog.CF_DATA, contentData);

        // error列族数据
        Map<String, byte[]> errorData = new HashMap<>();
        putIfNotNull(errorData, "exceptionInfo", actionLog.getExceptionInfo());

        familyMap.put(ActionLog.CF_ERROR, errorData);

        return familyMap;
    }

    /**
     * 将值放入Map，如果值不为null
     */
    private static void putIfNotNull(Map<String, byte[]> map, String key, Object value) {
        if (value != null) {
            map.put(key, Bytes.toBytes(value.toString()));
        }
    }

    /**
     * 格式化LocalDateTime为字符串
     */
    private static String formatDateTime(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.format(DATE_TIME_FORMATTER) : null;
    }

    /**
     * 解析字符串为LocalDateTime
     */
    public static LocalDateTime parseDateTime(String dateTimeStr) {
        return dateTimeStr != null && !dateTimeStr.isEmpty() ?
            LocalDateTime.parse(dateTimeStr, DATE_TIME_FORMATTER) : null;
    }

    /**
     * 从HBase数据重构AutoTaskLog对象
     *
     * @param familyMap HBase数据Map
     * @return AutoTaskLog对象
     */
    public static AutoTaskLog buildAutoTaskLogFromHBaseData(Map<String, Map<String, byte[]>> familyMap) {
        AutoTaskLog.AutoTaskLogBuilder builder = AutoTaskLog.builder();


        // 处理content列族数据
        Map<String, byte[]> contentData = familyMap.get(AutoTaskLog.CF_DATA);
        if (contentData != null) {
            builder.applyJson(getStringValue(contentData, "applyJson"))
                .feedbackJson(getStringValue(contentData, "feedbackJson"))
                .resultStr(getStringValue(contentData, "resultStr"));
        }

        return builder.build();
    }

    /**
     * 从HBase数据重构ActionLog对象
     *
     * @param familyMap HBase数据Map
     * @return ActionLog对象
     */
    public static ActionLog buildActionLogFromHBaseData(Map<String, Map<String, byte[]>> familyMap) {
        ActionLog.ActionLogBuilder builder = ActionLog.builder();

        // 处理info列族数据
        Map<String, byte[]> infoData = familyMap.get(ActionLog.CF_INFO);
        if (infoData != null) {
            builder.actionId(getStringValue(infoData, "actionId"))
                .autoTraceId(getStringValue(infoData, "autoTraceId"))
                .actionName(getStringValue(infoData, "actionName"))
                .url(getStringValue(infoData, "url"))
                .sendTime(parseDateTime(getStringValue(infoData, "sendTime")))
                .receiveTime(parseDateTime(getStringValue(infoData, "receiveTime")));
        }

        // 处理data列族数据
        Map<String, byte[]> contentData = familyMap.get(ActionLog.CF_DATA);
        if (contentData != null) {
            builder.requestBody(getStringValue(contentData, "requestBody"))
                .responseBody(getStringValue(contentData, "responseBody"))
                .inTaskBody(getStringValue(contentData, "inTaskBody"))
                .outTaskBody(getStringValue(contentData, "outTaskBody"))
                .exceptionInfo(getStringValue(contentData, "exceptionInfo"));
        }

        Map<String, byte[]> errorData = familyMap.get(ActionLog.CF_ERROR);
        if (errorData != null) {
            builder.exceptionInfo(getStringValue(errorData, "exceptionInfo"));
        }

        return builder.build();
    }

    /**
     * 从byte数组获取字符串值
     */
    private static String getStringValue(Map<String, byte[]> data, String key) {
        byte[] value = data.get(key);
        return value != null ? Bytes.toString(value) : null;
    }
}
